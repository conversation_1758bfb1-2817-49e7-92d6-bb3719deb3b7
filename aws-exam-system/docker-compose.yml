services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: aws-exam-postgres
    environment:
      POSTGRES_DB: aws_exam_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - aws-exam-network

  # Next.js应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aws-exam-app
    environment:
      DATABASE_URL: ***********************************************/aws_exam_system?schema=public
      NODE_ENV: production
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - aws-exam-network
    volumes:
      - ./aws_quiz_final.json:/app/aws_quiz_final.json:ro

  # pgAdmin管理界面（可选）
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aws-exam-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - aws-exam-network
    profiles:
      - admin

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  aws-exam-network:
    driver: bridge
