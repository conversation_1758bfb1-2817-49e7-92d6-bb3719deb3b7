version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: aws-exam-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: aws_exam_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - aws-exam-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d aws_exam_system"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 可选：添加 pgAdmin 用于数据库管理
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aws-exam-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - aws-exam-network
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  aws-exam-network:
    driver: bridge
