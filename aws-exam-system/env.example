# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# SQLite (开发环境备用)
# DATABASE_URL="file:./dev.db"

# PostgreSQL (生产环境)
DATABASE_URL="postgresql://postgres:postgres123@localhost:5432/aws_exam_system?schema=public"