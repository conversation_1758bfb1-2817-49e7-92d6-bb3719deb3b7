'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { getAllQuestions, updateQuestionAnswer, recalculateAllExamResults } from '@/lib/actions/exam-actions'
import { Search, Edit, Save, X, RefreshCw } from 'lucide-react'

interface Question {
  id: string
  questionNumber: number
  questionText: string
  options: string[]
  correctAnswer: string
  isMultipleChoice: boolean
  multipleChoiceCount?: number
  category: string
  difficulty: string
}

export default function QuestionsAdminPage() {
  const [questions, setQuestions] = useState<Question[]>([])
  const [filteredQuestions, setFilteredQuestions] = useState<Question[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [editingQuestion, setEditingQuestion] = useState<string | null>(null)
  const [editForm, setEditForm] = useState<Partial<Question>>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [recalculating, setRecalculating] = useState(false)

  useEffect(() => {
    loadQuestions()
  }, [])

  useEffect(() => {
    const filtered = questions.filter(q => 
      q.questionText.toLowerCase().includes(searchTerm.toLowerCase()) ||
      q.questionNumber.toString().includes(searchTerm) ||
      q.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredQuestions(filtered)
  }, [questions, searchTerm])

  const loadQuestions = async () => {
    try {
      const result = await getAllQuestions()
      if (result.success) {
        setQuestions(result.questions)
      } else {
        console.error('加载题目失败:', result.error)
        alert('加载题目失败: ' + result.error)
      }
    } catch (error) {
      console.error('加载题目失败:', error)
      alert('加载题目失败')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (question: Question) => {
    setEditingQuestion(question.id)
    setEditForm(question)
  }

  const handleSave = async () => {
    if (!editingQuestion || !editForm.correctAnswer) return

    setSaving(true)
    try {
      const result = await updateQuestionAnswer(editingQuestion, editForm.correctAnswer)
      if (result.success) {
        // 更新本地状态
        setQuestions(prev => prev.map(q =>
          q.id === editingQuestion ? { ...q, ...editForm } as Question : q
        ))

        setEditingQuestion(null)
        setEditForm({})
        alert('题目修改已保存')
      } else {
        alert('保存失败: ' + result.error)
      }
    } catch (error) {
      console.error('保存失败:', error)
      alert('保存失败')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setEditingQuestion(null)
    setEditForm({})
  }

  const handleRecalculateResults = async () => {
    if (!confirm('确定要重新计算所有考试结果吗？这可能需要一些时间。')) {
      return
    }

    setRecalculating(true)
    try {
      const result = await recalculateAllExamResults()
      if (result.success) {
        alert(result.message || '所有考试结果已重新计算完成')
      } else {
        alert('重新计算失败: ' + result.error)
      }
    } catch (error) {
      console.error('重新计算失败:', error)
      alert('重新计算失败')
    } finally {
      setRecalculating(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载题目中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                题目管理
              </h1>
              <p className="text-gray-600">
                管理和校准考试题目的正确答案
              </p>
            </div>
            
            <Button
              onClick={handleRecalculateResults}
              disabled={recalculating}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${recalculating ? 'animate-spin' : ''}`} />
              {recalculating ? '重新计算中...' : '重新计算所有结果'}
            </Button>
          </div>

          {/* Search */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索题目编号、内容或分类..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="text-sm text-gray-600">
                共 {filteredQuestions.length} 道题目
              </div>
            </div>
          </div>

          {/* Questions List */}
          <div className="space-y-4">
            {filteredQuestions.map((question) => (
              <div key={question.id} className="bg-white rounded-lg shadow-sm border border-gray-200">
                {editingQuestion === question.id ? (
                  // Edit Mode
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium text-gray-500">
                          题目 {question.questionNumber}
                        </span>
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                          {question.category}
                        </span>
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                          {question.difficulty}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          onClick={handleSave}
                          disabled={saving}
                        >
                          <Save className="w-4 h-4 mr-1" />
                          {saving ? '保存中...' : '保存'}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleCancel}
                        >
                          <X className="w-4 h-4 mr-1" />
                          取消
                        </Button>
                      </div>
                    </div>

                    <div className="mb-4">
                      <p className="text-gray-900 leading-relaxed">
                        {question.questionText}
                      </p>
                    </div>

                    <div className="space-y-2 mb-4">
                      {question.options.map((option, index) => (
                        <div key={index} className="text-gray-700">
                          {option}
                        </div>
                      ))}
                    </div>

                    <div className="bg-yellow-50 rounded-lg p-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        正确答案 (当前: {question.correctAnswer})
                      </label>
                      <input
                        type="text"
                        value={editForm.correctAnswer || ''}
                        onChange={(e) => setEditForm(prev => ({
                          ...prev,
                          correctAnswer: e.target.value.toUpperCase()
                        }))}
                        placeholder="输入正确答案，如: A 或 AB"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        单选题输入单个字母(如: A)，多选题输入多个字母(如: AB)
                      </p>
                    </div>
                  </div>
                ) : (
                  // View Mode
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium text-gray-500">
                          题目 {question.questionNumber}
                        </span>
                        {question.isMultipleChoice && (
                          <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded">
                            多选题
                          </span>
                        )}
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                          {question.category}
                        </span>
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                          {question.difficulty}
                        </span>
                      </div>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(question)}
                      >
                        <Edit className="w-4 h-4 mr-1" />
                        编辑
                      </Button>
                    </div>

                    <div className="mb-4">
                      <p className="text-gray-900 leading-relaxed">
                        {question.questionText}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">选项:</h4>
                        <div className="space-y-1">
                          {question.options.map((option, index) => (
                            <div key={index} className="text-sm text-gray-600">
                              {option}
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">正确答案:</h4>
                        <div className="text-lg font-bold text-green-600">
                          {question.correctAnswer}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredQuestions.length === 0 && (
            <div className="bg-white rounded-lg p-12 text-center shadow-sm">
              <div className="text-gray-400 mb-4">
                <Search className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                没有找到匹配的题目
              </h3>
              <p className="text-gray-600">
                请尝试其他搜索关键词
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
