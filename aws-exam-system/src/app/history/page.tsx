'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { formatTime, formatPercentage } from '@/lib/utils'
import { getExamHistory } from '@/lib/actions/exam-actions'
import { Calendar, Clock, Target, Eye, Home } from 'lucide-react'

interface ExamHistory {
  id: string
  title: string
  type: string
  status: string
  score: number | null
  totalQuestions: number
  correctCount: number | null
  timeSpent: number | null
  createdAt: string
  completedAt: string | null
}

export default function HistoryPage() {
  const [exams, setExams] = useState<ExamHistory[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadExamHistory()
  }, [])

  const loadExamHistory = async () => {
    try {
      // 这里应该调用API获取历史考试数据
      // 暂时使用模拟数据
      const mockData: ExamHistory[] = [
        {
          id: 'exam1',
          title: '模拟考试 #1',
          type: 'simulation',
          status: 'completed',
          score: 78.5,
          totalQuestions: 65,
          correctCount: 51,
          timeSpent: 7200, // 2小时
          createdAt: '2024-01-15T10:00:00Z',
          completedAt: '2024-01-15T12:00:00Z'
        },
        {
          id: 'exam2',
          title: '练习模式 - Storage专项',
          type: 'practice',
          status: 'completed',
          score: 85.0,
          totalQuestions: 20,
          correctCount: 17,
          timeSpent: 1800, // 30分钟
          createdAt: '2024-01-14T14:30:00Z',
          completedAt: '2024-01-14T15:00:00Z'
        },
        {
          id: 'exam3',
          title: '错题复习',
          type: 'review',
          status: 'completed',
          score: 70.0,
          totalQuestions: 10,
          correctCount: 7,
          timeSpent: 900, // 15分钟
          createdAt: '2024-01-13T16:00:00Z',
          completedAt: '2024-01-13T16:15:00Z'
        }
      ]
      setExams(mockData)
    } catch (error) {
      console.error('加载历史考试失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getExamTypeLabel = (type: string) => {
    switch (type) {
      case 'simulation': return '模拟考试'
      case 'practice': return '练习模式'
      case 'review': return '错题复习'
      default: return type
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100'
    if (score >= 60) return 'bg-yellow-100'
    return 'bg-red-100'
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载历史记录中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                考试历史
              </h1>
              <p className="text-gray-600">
                查看您的考试记录和详细分析
              </p>
            </div>
            <Link href="/">
              <Button variant="outline" className="flex items-center gap-2">
                <Home className="w-4 h-4" />
                返回首页
              </Button>
            </Link>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {exams.length}
              </div>
              <div className="text-gray-600">总考试次数</div>
            </div>
            
            <div className="bg-white rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {exams.filter(e => e.score && e.score >= 72).length}
              </div>
              <div className="text-gray-600">通过次数</div>
            </div>
            
            <div className="bg-white rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {exams.length > 0 ? Math.round(exams.reduce((sum, e) => sum + (e.score || 0), 0) / exams.length) : 0}%
              </div>
              <div className="text-gray-600">平均分数</div>
            </div>
            
            <div className="bg-white rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {Math.round(exams.reduce((sum, e) => sum + (e.timeSpent || 0), 0) / 3600)}h
              </div>
              <div className="text-gray-600">总学习时间</div>
            </div>
          </div>

          {/* Exam List */}
          <div className="space-y-4">
            {exams.length === 0 ? (
              <div className="bg-white rounded-lg p-12 text-center shadow-sm">
                <div className="text-gray-400 mb-4">
                  <Target className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  暂无考试记录
                </h3>
                <p className="text-gray-600 mb-6">
                  开始您的第一次考试吧！
                </p>
                <Link href="/exam/create">
                  <Button>开始考试</Button>
                </Link>
              </div>
            ) : (
              exams.map((exam) => (
                <div key={exam.id} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {exam.title}
                        </h3>
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                          {getExamTypeLabel(exam.type)}
                        </span>
                        {exam.status === 'completed' && exam.score !== null && (
                          <span className={`px-2 py-1 text-xs rounded ${getScoreBgColor(exam.score)} ${getScoreColor(exam.score)}`}>
                            {exam.score >= 72 ? '通过' : '未通过'}
                          </span>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {new Date(exam.createdAt).toLocaleDateString('zh-CN')}
                        </div>
                        
                        {exam.timeSpent && (
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {formatTime(exam.timeSpent)}
                          </div>
                        )}
                        
                        <div className="flex items-center gap-1">
                          <Target className="w-4 h-4" />
                          {exam.correctCount || 0}/{exam.totalQuestions}
                        </div>
                        
                        {exam.score !== null && (
                          <div className={`flex items-center gap-1 font-medium ${getScoreColor(exam.score)}`}>
                            <span>分数: {Math.round(exam.score)}%</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      {exam.status === 'completed' && (
                        <>
                          <Link href={`/exam/${exam.id}/result`}>
                            <Button variant="outline" size="sm">
                              查看结果
                            </Button>
                          </Link>
                          <Link href={`/history/${exam.id}/review`}>
                            <Button size="sm" className="flex items-center gap-1">
                              <Eye className="w-4 h-4" />
                              题目回顾
                            </Button>
                          </Link>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
