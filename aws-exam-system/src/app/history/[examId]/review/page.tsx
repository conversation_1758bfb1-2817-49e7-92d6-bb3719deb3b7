'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { CheckCircle, XCircle, ArrowLeft, ArrowRight, Home, Settings } from 'lucide-react'

interface QuestionReview {
  id: string
  questionNumber: number
  questionText: string
  options: string[]
  correctAnswer: string
  userAnswer: string
  isCorrect: boolean
  isMultipleChoice: boolean
  multipleChoiceCount?: number
  category: string
  difficulty: string
  explanation?: string
}

interface ExamReviewProps {
  params: {
    examId: string
  }
}

export default function ExamReviewPage({ params }: ExamReviewProps) {
  const router = useRouter()
  const [questions, setQuestions] = useState<QuestionReview[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [filter, setFilter] = useState<'all' | 'correct' | 'incorrect'>('all')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadExamReview()
  }, [params.examId])

  const loadExamReview = async () => {
    try {
      // 这里应该调用API获取考试回顾数据
      // 暂时使用模拟数据
      const mockData: QuestionReview[] = [
        {
          id: 'q1',
          questionNumber: 1,
          questionText: '您需要为一个Web应用程序设计高可用性架构。该应用程序需要在多个可用区中运行，并且需要自动扩展功能。以下哪些AWS服务最适合这个需求？',
          options: [
            'A. Amazon EC2 Auto Scaling',
            'B. Elastic Load Balancer',
            'C. Amazon RDS Multi-AZ',
            'D. Amazon S3',
            'E. Amazon CloudFront'
          ],
          correctAnswer: 'AB',
          userAnswer: 'ABC',
          isCorrect: false,
          isMultipleChoice: true,
          multipleChoiceCount: 2,
          category: 'Compute',
          difficulty: 'Medium',
          explanation: 'EC2 Auto Scaling和ELB是构建高可用性Web应用的核心服务。RDS Multi-AZ虽然提供高可用性，但题目重点是Web应用层面的架构。'
        },
        {
          id: 'q2',
          questionNumber: 2,
          questionText: '您的公司需要存储大量的静态网站内容，并希望实现全球快速访问。最佳的AWS解决方案是什么？',
          options: [
            'A. Amazon S3 + CloudFront',
            'B. Amazon EFS + EC2',
            'C. Amazon EBS + EC2',
            'D. Amazon Glacier'
          ],
          correctAnswer: 'A',
          userAnswer: 'A',
          isCorrect: true,
          isMultipleChoice: false,
          category: 'Storage',
          difficulty: 'Easy'
        },
        {
          id: 'q3',
          questionNumber: 3,
          questionText: '以下哪个服务可以帮助您监控AWS资源的性能和运行状况？',
          options: [
            'A. AWS Config',
            'B. Amazon CloudWatch',
            'C. AWS CloudTrail',
            'D. AWS X-Ray'
          ],
          correctAnswer: 'B',
          userAnswer: 'C',
          isCorrect: false,
          isMultipleChoice: false,
          category: 'Monitoring',
          difficulty: 'Easy',
          explanation: 'CloudWatch是AWS的监控服务，用于收集和跟踪指标、监控日志文件、设置警报。CloudTrail主要用于API调用审计。'
        }
      ]
      setQuestions(mockData)
    } catch (error) {
      console.error('加载考试回顾失败:', error)
      alert('加载考试回顾失败')
      router.push('/history')
    } finally {
      setLoading(false)
    }
  }

  const filteredQuestions = questions.filter(q => {
    if (filter === 'correct') return q.isCorrect
    if (filter === 'incorrect') return !q.isCorrect
    return true
  })

  const currentQuestion = filteredQuestions[currentIndex]

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const handleNext = () => {
    if (currentIndex < filteredQuestions.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const getAnswerStatus = (optionLetter: string) => {
    const isCorrect = currentQuestion.correctAnswer.includes(optionLetter)
    const isUserSelected = currentQuestion.userAnswer.includes(optionLetter)
    
    if (isCorrect && isUserSelected) return 'correct-selected'
    if (isCorrect && !isUserSelected) return 'correct-missed'
    if (!isCorrect && isUserSelected) return 'incorrect-selected'
    return 'normal'
  }

  const getOptionStyle = (status: string) => {
    switch (status) {
      case 'correct-selected':
        return 'border-green-500 bg-green-50 text-green-900'
      case 'correct-missed':
        return 'border-green-500 bg-green-100 text-green-900'
      case 'incorrect-selected':
        return 'border-red-500 bg-red-50 text-red-900'
      default:
        return 'border-gray-200 bg-white text-gray-900'
    }
  }

  const getOptionIcon = (status: string) => {
    switch (status) {
      case 'correct-selected':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'correct-missed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'incorrect-selected':
        return <XCircle className="w-5 h-5 text-red-600" />
      default:
        return null
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载题目回顾中...</p>
        </div>
      </div>
    )
  }

  if (questions.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">没有找到题目数据</p>
          <Link href="/history">
            <Button>返回历史记录</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/history">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  返回历史
                </Button>
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  题目回顾
                </h1>
                <p className="text-sm text-gray-600">
                  题目 {currentIndex + 1} / {filteredQuestions.length}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Filter */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">筛选:</span>
                <select
                  value={filter}
                  onChange={(e) => {
                    setFilter(e.target.value as any)
                    setCurrentIndex(0)
                  }}
                  className="text-sm border border-gray-300 rounded px-2 py-1"
                >
                  <option value="all">全部题目</option>
                  <option value="correct">答对题目</option>
                  <option value="incorrect">答错题目</option>
                </select>
              </div>
              
              <Link href="/">
                <Button variant="outline" size="sm">
                  <Home className="w-4 h-4 mr-2" />
                  首页
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Question Card */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            {/* Question Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-500">
                  题目 {currentQuestion.questionNumber}
                </span>
                {currentQuestion.isMultipleChoice && (
                  <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded">
                    多选题 (选择 {currentQuestion.multipleChoiceCount} 项)
                  </span>
                )}
                <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                  {currentQuestion.category}
                </span>
                <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                  {currentQuestion.difficulty}
                </span>
              </div>
              
              <div className={`flex items-center gap-2 ${
                currentQuestion.isCorrect ? 'text-green-600' : 'text-red-600'
              }`}>
                {currentQuestion.isCorrect ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <XCircle className="w-5 h-5" />
                )}
                <span className="font-medium">
                  {currentQuestion.isCorrect ? '答对' : '答错'}
                </span>
              </div>
            </div>

            {/* Question Text */}
            <div className="mb-6">
              <p className="text-gray-900 leading-relaxed">
                {currentQuestion.questionText}
              </p>
            </div>

            {/* Options */}
            <div className="space-y-3 mb-6">
              {currentQuestion.options.map((option, index) => {
                const optionLetter = option.charAt(0)
                const status = getAnswerStatus(optionLetter)
                
                return (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border ${getOptionStyle(status)}`}
                  >
                    <div className="flex items-start gap-3">
                      {getOptionIcon(status)}
                      <span className="flex-1">{option}</span>
                      {status === 'correct-missed' && (
                        <span className="text-xs text-green-600 font-medium">正确答案</span>
                      )}
                      {status === 'incorrect-selected' && (
                        <span className="text-xs text-red-600 font-medium">您的选择</span>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Answer Summary */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">正确答案: </span>
                  <span className="text-green-600 font-medium">
                    {currentQuestion.correctAnswer.split('').join(', ')}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">您的答案: </span>
                  <span className={`font-medium ${
                    currentQuestion.isCorrect ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {currentQuestion.userAnswer || '未作答'}
                  </span>
                </div>
              </div>
            </div>

            {/* Explanation */}
            {currentQuestion.explanation && (
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">解析</h4>
                <p className="text-blue-800 text-sm leading-relaxed">
                  {currentQuestion.explanation}
                </p>
              </div>
            )}
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentIndex === 0}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              上一题
            </Button>
            
            <div className="text-sm text-gray-600">
              {currentIndex + 1} / {filteredQuestions.length}
            </div>
            
            <Button
              onClick={handleNext}
              disabled={currentIndex === filteredQuestions.length - 1}
            >
              下一题
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
