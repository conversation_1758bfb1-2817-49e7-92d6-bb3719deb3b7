'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { createExam } from '@/lib/actions/exam-actions'
import { ExamType, QuestionCategory, QuestionDifficulty } from '@/types/exam'

const EXAM_CONFIGS = {
  simulation: {
    title: '模拟考试',
    description: '完全模拟真实AWS SAA-C03考试环境',
    questionCount: 65,
    timeLimit: 130,
    icon: '🎯'
  },
  practice: {
    title: '练习模式',
    description: '自定义题目数量和范围进行练习',
    questionCount: 20,
    timeLimit: undefined,
    icon: '📚'
  },
  review: {
    title: '错题复习',
    description: '针对错题进行专项复习',
    questionCount: 10,
    timeLimit: undefined,
    icon: '🔄'
  }
}

const CATEGORIES: QuestionCategory[] = [
  'Storage', 'Compute', 'Database', 'Serverless', 'Networking',
  'Security', 'Monitoring', 'Content Delivery', 'API Management',
  'Messaging', 'Management', 'Cost Management', 'General'
]

const DIFFICULTIES: QuestionDifficulty[] = ['Easy', 'Medium', 'Hard']

export default function CreateExamPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const examType = (searchParams.get('type') as ExamType) || 'practice'
  
  const [loading, setLoading] = useState(false)
  const [questionCount, setQuestionCount] = useState(EXAM_CONFIGS[examType].questionCount)
  const [timeLimit, setTimeLimit] = useState(EXAM_CONFIGS[examType].timeLimit)
  const [selectedCategories, setSelectedCategories] = useState<QuestionCategory[]>([])
  const [selectedDifficulties, setSelectedDifficulties] = useState<QuestionDifficulty[]>([])
  const [includeMultipleChoice, setIncludeMultipleChoice] = useState(true)

  const config = EXAM_CONFIGS[examType]

  const handleCreateExam = async () => {
    setLoading(true)
    try {
      const result = await createExam({
        type: examType,
        questionCount,
        timeLimit,
        categories: selectedCategories.length > 0 ? selectedCategories : undefined,
        difficulty: selectedDifficulties.length > 0 ? selectedDifficulties : undefined,
        includeMultipleChoice
      })

      if (result.success) {
        router.push(`/exam/${result.examId}`)
      } else {
        alert(result.error || '创建考试失败')
      }
    } catch (error) {
      console.error('创建考试失败:', error)
      alert('创建考试失败')
    } finally {
      setLoading(false)
    }
  }

  const toggleCategory = (category: QuestionCategory) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const toggleDifficulty = (difficulty: QuestionDifficulty) => {
    setSelectedDifficulties(prev => 
      prev.includes(difficulty) 
        ? prev.filter(d => d !== difficulty)
        : [...prev, difficulty]
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="text-6xl mb-4">{config.icon}</div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {config.title}
            </h1>
            <p className="text-gray-600">
              {config.description}
            </p>
          </div>

          {/* Configuration Form */}
          <div className="bg-white rounded-lg shadow-sm p-6 space-y-6">
            {/* Question Count */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                题目数量
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={questionCount}
                onChange={(e) => setQuestionCount(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={examType === 'simulation'}
              />
              {examType === 'simulation' && (
                <p className="text-sm text-gray-500 mt-1">
                  模拟考试固定为65题
                </p>
              )}
            </div>

            {/* Time Limit */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                时间限制（分钟）
              </label>
              <input
                type="number"
                min="0"
                value={timeLimit || ''}
                onChange={(e) => setTimeLimit(e.target.value ? parseInt(e.target.value) : undefined)}
                placeholder="不限时间"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={examType === 'simulation'}
              />
              {examType === 'simulation' && (
                <p className="text-sm text-gray-500 mt-1">
                  模拟考试固定为130分钟
                </p>
              )}
            </div>

            {/* Categories */}
            {examType !== 'simulation' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择分类（可选）
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {CATEGORIES.map(category => (
                    <button
                      key={category}
                      onClick={() => toggleCategory(category)}
                      className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                        selectedCategories.includes(category)
                          ? 'bg-blue-100 border-blue-300 text-blue-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  不选择则包含所有分类
                </p>
              </div>
            )}

            {/* Difficulties */}
            {examType !== 'simulation' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择难度（可选）
                </label>
                <div className="flex gap-2">
                  {DIFFICULTIES.map(difficulty => (
                    <button
                      key={difficulty}
                      onClick={() => toggleDifficulty(difficulty)}
                      className={`px-4 py-2 text-sm rounded-md border transition-colors ${
                        selectedDifficulties.includes(difficulty)
                          ? 'bg-blue-100 border-blue-300 text-blue-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {difficulty}
                    </button>
                  ))}
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  不选择则包含所有难度
                </p>
              </div>
            )}

            {/* Multiple Choice */}
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={includeMultipleChoice}
                  onChange={(e) => setIncludeMultipleChoice(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">
                  包含多选题
                </span>
              </label>
            </div>

            {/* Actions */}
            <div className="flex gap-4 pt-4">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="flex-1"
              >
                返回
              </Button>
              <Button
                onClick={handleCreateExam}
                disabled={loading}
                className="flex-1"
              >
                {loading ? '创建中...' : '开始考试'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
