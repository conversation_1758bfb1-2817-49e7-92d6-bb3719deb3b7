'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { getExam, submitAnswer, completeExam } from '@/lib/actions/exam-actions'
import { formatTime } from '@/lib/utils'
import { Clock, CheckCircle, Circle, Flag } from 'lucide-react'

interface ExamPageProps {
  params: {
    examId: string
  }
}

export default function ExamPage({ params }: ExamPageProps) {
  const router = useRouter()
  const [exam, setExam] = useState<any>(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({})
  const [markedQuestions, setMarkedQuestions] = useState<Set<string>>(new Set())
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    loadExam()
  }, [params.examId])

  useEffect(() => {
    if (exam?.timeLimit && timeRemaining === null) {
      setTimeRemaining(exam.timeLimit * 60) // 转换为秒
    }
  }, [exam])

  useEffect(() => {
    if (timeRemaining !== null && timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev === null || prev <= 1) {
            handleCompleteExam()
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [timeRemaining])

  const loadExam = async () => {
    try {
      const result = await getExam(params.examId)
      if (result.success) {
        setExam(result.exam)
        // 初始化已有答案
        const answers: Record<string, string> = {}
        result.exam.userAnswers?.forEach((answer: any) => {
          answers[answer.questionId] = answer.userAnswer
        })
        setUserAnswers(answers)
      } else {
        alert(result.error || '加载考试失败')
        router.push('/')
      }
    } catch (error) {
      console.error('加载考试失败:', error)
      alert('加载考试失败')
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  const handleAnswerSelect = async (answer: string) => {
    const currentQuestion = exam.questions[currentQuestionIndex]

    let newAnswer: string

    if (currentQuestion.isMultipleChoice) {
      // 多选题逻辑
      const currentAnswers = userAnswers[currentQuestion.id] || ''
      const selectedOptions = currentAnswers.split('').filter(Boolean)

      if (selectedOptions.includes(answer)) {
        // 取消选择
        newAnswer = selectedOptions.filter(opt => opt !== answer).join('')
      } else {
        // 添加选择
        newAnswer = [...selectedOptions, answer].sort().join('')
      }
    } else {
      // 单选题逻辑
      newAnswer = answer
    }

    // 更新本地状态
    setUserAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: newAnswer
    }))

    // 提交到服务器
    try {
      await submitAnswer(params.examId, currentQuestion.id, newAnswer)
    } catch (error) {
      console.error('提交答案失败:', error)
    }
  }

  const handleNextQuestion = () => {
    if (currentQuestionIndex < exam.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
    }
  }

  const handlePrevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }

  const handleQuestionJump = (index: number) => {
    setCurrentQuestionIndex(index)
  }

  const toggleMarkQuestion = () => {
    const currentQuestion = exam.questions[currentQuestionIndex]
    setMarkedQuestions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(currentQuestion.id)) {
        newSet.delete(currentQuestion.id)
      } else {
        newSet.add(currentQuestion.id)
      }
      return newSet
    })
  }

  const handleCompleteExam = async () => {
    if (submitting) return
    
    setSubmitting(true)
    try {
      const result = await completeExam(params.examId)
      if (result.success) {
        router.push(`/exam/${params.examId}/result`)
      } else {
        alert(result.error || '完成考试失败')
      }
    } catch (error) {
      console.error('完成考试失败:', error)
      alert('完成考试失败')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载考试中...</p>
        </div>
      </div>
    )
  }

  if (!exam) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">考试不存在</p>
          <Button onClick={() => router.push('/')} className="mt-4">
            返回首页
          </Button>
        </div>
      </div>
    )
  }

  const currentQuestion = exam.questions[currentQuestionIndex]
  const answeredCount = Object.keys(userAnswers).length
  const progress = (answeredCount / exam.questions.length) * 100

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {exam.title}
              </h1>
              <p className="text-sm text-gray-600">
                题目 {currentQuestionIndex + 1} / {exam.questions.length}
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              {timeRemaining !== null && (
                <div className="flex items-center gap-2 text-orange-600">
                  <Clock className="w-5 h-5" />
                  <span className="font-mono text-lg">
                    {formatTime(timeRemaining)}
                  </span>
                </div>
              )}
              
              <div className="text-sm text-gray-600">
                已答: {answeredCount} / {exam.questions.length}
              </div>
              
              <Button
                onClick={handleCompleteExam}
                disabled={submitting}
                variant="outline"
              >
                {submitting ? '提交中...' : '完成考试'}
              </Button>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Question Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm p-6">
              {/* Question Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-500">
                    题目 {currentQuestion.questionNumber}
                  </span>
                  {currentQuestion.isMultipleChoice && (
                    <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded">
                      多选题 (选择 {currentQuestion.multipleChoiceCount} 项)
                    </span>
                  )}
                  {currentQuestion.category && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                      {currentQuestion.category}
                    </span>
                  )}
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleMarkQuestion}
                  className={markedQuestions.has(currentQuestion.id) ? 'text-orange-600' : 'text-gray-400'}
                >
                  <Flag className="w-4 h-4" />
                </Button>
              </div>

              {/* Question Text */}
              <div className="mb-6">
                <p className="text-gray-900 leading-relaxed">
                  {currentQuestion.questionText}
                </p>
              </div>

              {/* Options */}
              <div className="space-y-3">
                {currentQuestion.options.map((option: string, index: number) => {
                  const optionLetter = option.charAt(0)
                  const currentAnswers = userAnswers[currentQuestion.id] || ''
                  const isSelected = currentQuestion.isMultipleChoice
                    ? currentAnswers.includes(optionLetter)
                    : currentAnswers === optionLetter

                  return (
                    <button
                      key={index}
                      onClick={() => handleAnswerSelect(optionLetter)}
                      className={`w-full text-left p-4 rounded-lg border transition-colors ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        {currentQuestion.isMultipleChoice ? (
                          // 多选题使用方形复选框
                          <div className={`w-5 h-5 border-2 flex items-center justify-center mt-0.5 rounded ${
                            isSelected ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
                          }`}>
                            {isSelected && (
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>
                        ) : (
                          // 单选题使用圆形单选框
                          <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center mt-0.5 ${
                            isSelected ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
                          }`}>
                            {isSelected && <div className="w-2 h-2 bg-white rounded-full"></div>}
                          </div>
                        )}
                        <span className="text-gray-900">{option}</span>
                      </div>
                    </button>
                  )
                })}
              </div>

              {/* Navigation */}
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrevQuestion}
                  disabled={currentQuestionIndex === 0}
                >
                  上一题
                </Button>
                
                <Button
                  onClick={handleNextQuestion}
                  disabled={currentQuestionIndex === exam.questions.length - 1}
                >
                  下一题
                </Button>
              </div>
            </div>
          </div>

          {/* Question Navigator */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4 sticky top-4">
              <h3 className="font-medium text-gray-900 mb-4">题目导航</h3>
              <div className="grid grid-cols-5 gap-2">
                {exam.questions.map((question: any, index: number) => {
                  const isAnswered = userAnswers[question.id]
                  const isMarked = markedQuestions.has(question.id)
                  const isCurrent = index === currentQuestionIndex
                  
                  return (
                    <button
                      key={question.id}
                      onClick={() => handleQuestionJump(index)}
                      className={`w-8 h-8 text-xs rounded flex items-center justify-center relative ${
                        isCurrent
                          ? 'bg-blue-600 text-white'
                          : isAnswered
                          ? 'bg-green-100 text-green-700'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {index + 1}
                      {isMarked && (
                        <Flag className="w-2 h-2 absolute -top-1 -right-1 text-orange-500" />
                      )}
                    </button>
                  )
                })}
              </div>
              
              <div className="mt-4 space-y-2 text-xs">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-600 rounded"></div>
                  <span>当前题目</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                  <span>已答题目</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gray-100 border border-gray-300 rounded"></div>
                  <span>未答题目</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
