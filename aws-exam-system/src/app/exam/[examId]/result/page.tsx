'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { getExamResult } from '@/lib/actions/exam-actions'
import { formatTime, formatPercentage } from '@/lib/utils'
import { Trophy, Target, Clock, BarChart3, Home, RotateCcw } from 'lucide-react'

interface ResultPageProps {
  params: {
    examId: string
  }
}

export default function ResultPage({ params }: ResultPageProps) {
  const router = useRouter()
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadResult()
  }, [params.examId])

  const loadResult = async () => {
    try {
      const response = await getExamResult(params.examId)
      if (response.success) {
        setResult(response.result)
      } else {
        alert(response.error || '加载结果失败')
        router.push('/')
      }
    } catch (error) {
      console.error('加载结果失败:', error)
      alert('加载结果失败')
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载结果中...</p>
        </div>
      </div>
    )
  }

  if (!result) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">结果不存在</p>
          <Button onClick={() => router.push('/')} className="mt-4">
            返回首页
          </Button>
        </div>
      </div>
    )
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100'
    if (score >= 60) return 'bg-yellow-100'
    return 'bg-red-100'
  }

  const isPassed = result.score >= 72 // AWS SAA-C03 passing score

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-4 ${
              isPassed ? 'bg-green-100' : 'bg-red-100'
            }`}>
              <Trophy className={`w-10 h-10 ${isPassed ? 'text-green-600' : 'text-red-600'}`} />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              考试完成！
            </h1>
            <p className={`text-xl ${isPassed ? 'text-green-600' : 'text-red-600'}`}>
              {isPassed ? '🎉 恭喜通过！' : '💪 继续努力！'}
            </p>
          </div>

          {/* Score Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className={`bg-white rounded-lg p-6 text-center shadow-sm ${getScoreBgColor(result.score)}`}>
              <div className={`text-3xl font-bold mb-2 ${getScoreColor(result.score)}`}>
                {Math.round(result.score)}%
              </div>
              <div className="text-gray-600">总分</div>
            </div>
            
            <div className="bg-white rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {result.correctCount}
              </div>
              <div className="text-gray-600">正确题数</div>
            </div>
            
            <div className="bg-white rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {result.totalQuestions}
              </div>
              <div className="text-gray-600">总题数</div>
            </div>
            
            <div className="bg-white rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {formatTime(result.timeSpent)}
              </div>
              <div className="text-gray-600">用时</div>
            </div>
          </div>

          {/* Category Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                分类分析
              </h2>
              <div className="space-y-4">
                {result.categoryResults.map((category: any) => (
                  <div key={category.category}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">
                        {category.category}
                      </span>
                      <span className="text-sm text-gray-600">
                        {category.correct}/{category.total} ({formatPercentage(category.percentage)})
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          category.percentage >= 80 ? 'bg-green-500' :
                          category.percentage >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${category.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Difficulty Analysis */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Target className="w-5 h-5" />
                难度分析
              </h2>
              <div className="space-y-4">
                {result.difficultyResults.map((difficulty: any) => (
                  <div key={difficulty.difficulty}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">
                        {difficulty.difficulty}
                      </span>
                      <span className="text-sm text-gray-600">
                        {difficulty.correct}/{difficulty.total} ({formatPercentage(difficulty.percentage)})
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          difficulty.percentage >= 80 ? 'bg-green-500' :
                          difficulty.percentage >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${difficulty.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Performance Insights */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              📊 成绩分析
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">优势领域</h3>
                <div className="space-y-2">
                  {result.categoryResults
                    .filter((cat: any) => cat.percentage >= 80)
                    .slice(0, 3)
                    .map((cat: any) => (
                      <div key={cat.category} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-gray-700">{cat.category}</span>
                        <span className="text-sm text-green-600 ml-auto">
                          {formatPercentage(cat.percentage)}
                        </span>
                      </div>
                    ))}
                  {result.categoryResults.filter((cat: any) => cat.percentage >= 80).length === 0 && (
                    <p className="text-sm text-gray-500">暂无优势领域，继续努力！</p>
                  )}
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">需要改进</h3>
                <div className="space-y-2">
                  {result.categoryResults
                    .filter((cat: any) => cat.percentage < 60)
                    .slice(0, 3)
                    .map((cat: any) => (
                      <div key={cat.category} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="text-sm text-gray-700">{cat.category}</span>
                        <span className="text-sm text-red-600 ml-auto">
                          {formatPercentage(cat.percentage)}
                        </span>
                      </div>
                    ))}
                  {result.categoryResults.filter((cat: any) => cat.percentage < 60).length === 0 && (
                    <p className="text-sm text-gray-500">各领域表现均衡！</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/">
              <Button variant="outline" className="flex items-center gap-2">
                <Home className="w-4 h-4" />
                返回首页
              </Button>
            </Link>
            
            <Link href="/exam/create?type=practice">
              <Button className="flex items-center gap-2">
                <RotateCcw className="w-4 h-4" />
                继续练习
              </Button>
            </Link>
            
            {!isPassed && (
              <Link href="/exam/create?type=simulation">
                <Button variant="outline" className="flex items-center gap-2">
                  <Target className="w-4 h-4" />
                  重新模拟考试
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
