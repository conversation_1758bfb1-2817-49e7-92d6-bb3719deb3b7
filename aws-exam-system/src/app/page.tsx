import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Book<PERSON><PERSON>, Clock, Target, RotateCcw, History } from "lucide-react"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            AWS SAA-C03
            <span className="block text-blue-600">模拟考试系统</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            基于1,018道真题的专业AWS解决方案架构师认证考试练习平台
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-16">
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-blue-600 mb-2">1,018</div>
            <div className="text-gray-600">道真题</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-green-600 mb-2">13</div>
            <div className="text-gray-600">个分类</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-purple-600 mb-2">88%</div>
            <div className="text-gray-600">单选题</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-orange-600 mb-2">12%</div>
            <div className="text-gray-600">多选题</div>
          </div>
        </div>

        {/* Exam Modes */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* 模拟考试 */}
          <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6 mx-auto">
              <Target className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
              模拟考试
            </h3>
            <p className="text-gray-600 mb-6 text-center">
              65题，130分钟，完全模拟真实考试环境
            </p>
            <Link href="/exam/create?type=simulation" className="block">
              <Button className="w-full bg-blue-600 hover:bg-blue-700">
                开始模拟考试
              </Button>
            </Link>
          </div>

          {/* 练习模式 */}
          <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6 mx-auto">
              <BookOpen className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
              练习模式
            </h3>
            <p className="text-gray-600 mb-6 text-center">
              自定义题目数量和分类，灵活练习
            </p>
            <Link href="/exam/create?type=practice" className="block">
              <Button className="w-full bg-green-600 hover:bg-green-700">
                开始练习
              </Button>
            </Link>
          </div>

          {/* 分类练习 */}
          <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-6 mx-auto">
              <Clock className="w-8 h-8 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
              分类练习
            </h3>
            <p className="text-gray-600 mb-6 text-center">
              按AWS服务分类进行专项练习
            </p>
            <Link href="/practice/categories" className="block">
              <Button className="w-full bg-purple-600 hover:bg-purple-700">
                选择分类
              </Button>
            </Link>
          </div>

          {/* 错题复习 */}
          <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-6 mx-auto">
              <RotateCcw className="w-8 h-8 text-orange-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
              错题复习
            </h3>
            <p className="text-gray-600 mb-6 text-center">
              针对错题进行专项复习和强化
            </p>
            <Link href="/exam/create?type=review" className="block">
              <Button className="w-full bg-orange-600 hover:bg-orange-700">
                复习错题
              </Button>
            </Link>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-16 text-center">
          <div className="flex justify-center gap-4 mb-12">
            <Link href="/history">
              <Button variant="outline" className="flex items-center gap-2">
                <History className="w-4 h-4" />
                考试历史
              </Button>
            </Link>
            <Link href="/admin/questions">
              <Button variant="outline" className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                题目管理
              </Button>
            </Link>
          </div>
        </div>

        {/* Features */}
        <div className="mt-8 text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-8">
            系统特色
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="text-lg font-semibold mb-2">精准题库</h3>
              <p className="text-gray-600">
                基于最新AWS SAA-C03考试大纲，覆盖所有知识点
              </p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-4">📊</div>
              <h3 className="text-lg font-semibold mb-2">智能分析</h3>
              <p className="text-gray-600">
                详细的成绩分析和薄弱环节识别
              </p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-lg font-semibold mb-2">实时反馈</h3>
              <p className="text-gray-600">
                即时答题反馈和进度跟踪
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
