# AWS考试系统

基于Next.js + Prisma + PostgreSQL + Server Actions的AWS SAA-C03认证考试模拟系统。

## 功能特性

- 🎯 **多种考试模式**: 模拟考试、练习模式、复习模式
- 📊 **智能分析**: 详细的答题分析和成绩统计
- 🔄 **题目管理**: 支持单选题和多选题，按分类和难度筛选
- 📱 **响应式设计**: 支持桌面和移动设备
- 💾 **数据持久化**: 基于PostgreSQL数据库存储
- 🎨 **现代UI**: 使用Tailwind CSS和shadcn/ui组件
- 🐳 **容器化部署**: Docker Compose一键启动

## 技术栈

- **前端**: Next.js 15, React, TypeScript
- **样式**: Tailwind CSS, shadcn/ui
- **数据库**: PostgreSQL + Prisma ORM
- **状态管理**: React Server Actions
- **容器化**: Docker + Docker Compose
- **管理工具**: pgAdmin

## 快速开始

### 环境要求

- Node.js 18+
- pnpm (推荐)
- Docker & Docker Compose

### 一键启动

```bash
# 克隆项目
git clone <repository-url>
cd aws-exam-system

# 安装依赖
pnpm install

# 一键启动整个系统（数据库 + 应用）
pnpm run start:full
```

### 手动启动

```bash
# 1. 设置数据库
pnpm run db:setup

# 2. 启动应用
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 开始使用。

## 数据库管理

### PostgreSQL连接信息
- **主机**: localhost:5432
- **数据库**: aws_exam_system
- **用户名**: postgres
- **密码**: postgres123

### 管理界面
- **pgAdmin**: http://localhost:8080 (<EMAIL> / admin123)
- **Prisma Studio**: `pnpm run db:studio`

### 可用命令

| 命令 | 说明 |
|------|------|
| `pnpm dev` | 启动开发服务器 |
| `pnpm build` | 构建生产版本 |
| `pnpm start` | 启动生产服务器 |
| `pnpm run start:full` | 一键启动整个系统 |
| `pnpm run db:setup` | 初始化数据库和导入数据 |
| `pnpm run db:start` | 启动数据库服务 |
| `pnpm run db:stop` | 停止数据库服务 |
| `pnpm run db:reset` | 重置数据库 |
| `pnpm run db:import` | 重新导入考试数据 |
| `pnpm run db:studio` | 打开Prisma Studio |

## 项目结构

```
aws-exam-system/
├── src/                    # 源代码
│   ├── app/               # Next.js App Router
│   ├── components/        # React组件
│   ├── lib/              # 工具库和Server Actions
│   └── types/            # TypeScript类型定义
├── prisma/               # 数据库架构
├── scripts/              # 数据库脚本
│   ├── setup-database.sh # 数据库初始化脚本
│   ├── start-system.sh   # 系统启动脚本
│   └── import-questions.ts # 数据导入脚本
├── docker-compose.yml    # Docker服务配置
└── DATABASE_SETUP.md     # 数据库设置详细指南
```

## 数据库架构

- **Question**: 题目表，存储1018道AWS题目
- **Exam**: 考试表，记录考试会话
- **ExamQuestion**: 考试题目关联表
- **UserAnswer**: 用户答题记录表
- **User**: 用户表（预留）

## 题库统计

- **总题数**: 1,018题
- **分类**: 13个AWS服务分类
- **难度**: Easy(27), Medium(822), Hard(169)
- **题型**: 单选题(897), 多选题(121)

## 开发指南

### 数据库迁移

从SQLite迁移到PostgreSQL已完成，包含：
- Docker Compose配置
- 自动化设置脚本
- 数据批量导入优化
- 管理界面集成

### 添加新题目

1. 准备JSON格式的题目数据
2. 运行导入脚本：`pnpm run db:import`

### 自定义考试配置

编辑 `src/lib/exam-config.ts` 文件：

```typescript
export const EXAM_CONFIGS = {
  simulation: {
    questionCount: 65,
    timeLimit: 130,
    passingScore: 72
  },
  // ...
}
```

## 故障排除

详细的故障排除指南请参考 [DATABASE_SETUP.md](./DATABASE_SETUP.md)

## 部署

### 开发环境
使用Docker Compose进行本地开发

### 生产环境
1. 配置生产环境的PostgreSQL
2. 设置环境变量
3. 构建和部署应用

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License
