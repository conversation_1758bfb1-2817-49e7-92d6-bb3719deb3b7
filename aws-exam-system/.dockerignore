# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Next.js
.next/
out/

# Production
build

# Misc
.DS_Store
*.pem

# Debug
*.log

# Local development
.vscode/
.idea/

# Testing
coverage/

# Database
prisma/dev.db*
*.db
*.db-journal

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md

# Scripts (except the ones we need)
scripts/setup-database.sh
scripts/start-system.sh

# Init scripts
init-scripts/
