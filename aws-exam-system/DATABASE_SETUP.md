# AWS考试系统 - PostgreSQL数据库设置指南

## 🚀 快速开始

### 方法1: 一键启动（推荐）
```bash
pnpm run start:full
```

### 方法2: 手动设置
```bash
# 1. 设置数据库
pnpm run db:setup

# 2. 启动应用
pnpm dev
```

## 📋 系统要求

- Docker & Docker Compose
- Node.js 18+
- pnpm

## 🐘 PostgreSQL配置

### 数据库连接信息
- **主机**: localhost
- **端口**: 5432
- **数据库**: aws_exam_system
- **用户名**: postgres
- **密码**: postgres123

### 连接字符串
```
postgresql://postgres:postgres123@localhost:5432/aws_exam_system?schema=public
```

## 🔧 管理界面

### pgAdmin Web界面
- **URL**: http://localhost:8080
- **邮箱**: <EMAIL>
- **密码**: admin123

### Prisma Studio
```bash
pnpm run db:studio
```

## 📊 可用命令

| 命令 | 说明 |
|------|------|
| `pnpm run db:setup` | 初始化数据库和导入数据 |
| `pnpm run db:start` | 启动数据库服务 |
| `pnpm run db:stop` | 停止数据库服务 |
| `pnpm run db:reset` | 重置数据库（删除所有数据） |
| `pnpm run db:import` | 重新导入考试数据 |
| `pnpm run db:studio` | 打开Prisma Studio |
| `pnpm run start:full` | 一键启动整个系统 |

## 🔄 数据迁移

### 从SQLite迁移到PostgreSQL
1. 备份现有数据（如果需要）
2. 运行数据库设置：`pnpm run db:setup`
3. 数据会自动从 `aws_quiz_final.json` 重新导入

### 更新数据库架构
```bash
# 生成新的迁移
npx prisma migrate dev --name your_migration_name

# 或直接推送架构变更
npx prisma db push
```

## 🐛 故障排除

### PostgreSQL连接失败
```bash
# 检查Docker服务状态
docker-compose ps

# 查看PostgreSQL日志
docker-compose logs postgres

# 重启服务
pnpm run db:stop
pnpm run db:start
```

### 数据导入失败
```bash
# 重新导入数据
pnpm run db:import

# 完全重置数据库
pnpm run db:reset
```

### 端口冲突
如果5432或8080端口被占用，修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "5433:5432"  # PostgreSQL
  - "8081:80"    # pgAdmin
```

## 📁 文件结构

```
aws-exam-system/
├── docker-compose.yml          # Docker服务配置
├── prisma/
│   └── schema.prisma           # 数据库架构
├── scripts/
│   ├── setup-database.sh      # 数据库初始化脚本
│   ├── start-system.sh        # 系统启动脚本
│   └── import-questions.ts     # 数据导入脚本
└── .env                        # 环境变量配置
```

## 🔒 生产环境注意事项

1. **修改默认密码**：更改 `docker-compose.yml` 中的数据库密码
2. **环境变量**：使用 `.env.production` 配置生产环境变量
3. **数据持久化**：确保 `postgres_data` 卷正确挂载
4. **网络安全**：限制数据库端口访问权限
5. **备份策略**：设置定期数据库备份

## 📈 性能优化

- 使用连接池
- 配置适当的PostgreSQL参数
- 监控数据库性能
- 定期维护和清理

## 🆘 获取帮助

如果遇到问题，请检查：
1. Docker是否正常运行
2. 端口是否被占用
3. 环境变量是否正确配置
4. 数据文件 `aws_quiz_final.json` 是否存在
