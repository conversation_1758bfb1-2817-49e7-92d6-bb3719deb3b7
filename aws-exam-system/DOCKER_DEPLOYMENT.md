# Docker部署指南

本文档介绍如何使用Docker一键部署AWS考试系统。

## 🚀 快速开始

### 环境要求

- Docker 20.10+
- Docker Compose 2.0+

### 一键启动

```bash
# 方法1: 使用启动脚本（推荐）
./start-docker.sh

# 方法2: 使用npm脚本
pnpm run docker:start

# 方法3: 直接使用docker-compose
docker-compose up --build -d
```

### 访问应用

- **主应用**: http://localhost:3000
- **pgAdmin**: http://localhost:8080 (需要启用admin profile)

## 📋 可用命令

### 基础命令

```bash
# 构建镜像
pnpm run docker:build

# 启动服务
pnpm run docker:up

# 停止服务
pnpm run docker:down

# 查看日志
pnpm run docker:logs

# 重启服务
pnpm run docker:restart
```

### 管理命令

```bash
# 启用pgAdmin管理界面
pnpm run docker:admin

# 完全重置（删除所有数据）
docker-compose down -v
pnpm run docker:up

# 仅重启应用容器
docker-compose restart app

# 查看容器状态
docker-compose ps
```

## 🏗️ 架构说明

### 服务组件

1. **app**: Next.js应用容器
   - 端口: 3000
   - 自动数据库迁移和数据导入
   - 生产优化构建

2. **postgres**: PostgreSQL数据库
   - 端口: 5432
   - 数据持久化存储
   - 健康检查

3. **pgadmin**: 数据库管理界面（可选）
   - 端口: 8080
   - 用户名: <EMAIL>
   - 密码: admin123

### 数据持久化

- PostgreSQL数据存储在Docker volume中
- 容器重启不会丢失数据
- 使用`docker-compose down -v`可以删除所有数据

## 🔧 配置说明

### 环境变量

应用容器使用以下环境变量：

```env
DATABASE_URL=***********************************************/aws_exam_system?schema=public
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
```

### 数据库配置

```env
POSTGRES_DB=aws_exam_system
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :5432
   
   # 修改docker-compose.yml中的端口映射
   ```

2. **数据库连接失败**
   ```bash
   # 查看数据库日志
   docker-compose logs postgres
   
   # 重启数据库
   docker-compose restart postgres
   ```

3. **应用启动失败**
   ```bash
   # 查看应用日志
   docker-compose logs app
   
   # 重新构建镜像
   docker-compose build --no-cache app
   ```

4. **数据导入失败**
   ```bash
   # 手动导入数据
   docker-compose exec app npx tsx scripts/import-questions.ts
   ```

### 清理和重置

```bash
# 停止所有服务
docker-compose down

# 删除所有数据
docker-compose down -v

# 删除镜像
docker-compose down --rmi all

# 完全清理
docker system prune -a
```

## 📊 监控和日志

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f app
docker-compose logs -f postgres

# 查看最近的日志
docker-compose logs --tail=100 app
```

### 容器状态

```bash
# 查看容器状态
docker-compose ps

# 查看资源使用
docker stats

# 进入容器
docker-compose exec app sh
docker-compose exec postgres psql -U postgres -d aws_exam_system
```

## 🚀 生产部署

### 安全配置

1. **修改默认密码**
   ```yaml
   # 在docker-compose.yml中修改
   POSTGRES_PASSWORD: your_secure_password
   PGADMIN_DEFAULT_PASSWORD: your_admin_password
   ```

2. **网络安全**
   ```yaml
   # 移除不必要的端口暴露
   # 使用反向代理（如Nginx）
   ```

3. **数据备份**
   ```bash
   # 定期备份数据库
   docker-compose exec postgres pg_dump -U postgres aws_exam_system > backup.sql
   ```

### 性能优化

1. **资源限制**
   ```yaml
   services:
     app:
       deploy:
         resources:
           limits:
             memory: 512M
             cpus: '0.5'
   ```

2. **健康检查**
   ```yaml
   healthcheck:
     test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
     interval: 30s
     timeout: 10s
     retries: 3
   ```

## 📝 开发模式

如果需要在Docker环境中进行开发：

```bash
# 使用开发模式启动
docker-compose -f docker-compose.dev.yml up
```

创建`docker-compose.dev.yml`用于开发环境配置。
