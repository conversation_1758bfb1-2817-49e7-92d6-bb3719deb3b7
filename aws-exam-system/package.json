{"name": "aws-exam-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:setup": "./scripts/setup-database.sh", "db:start": "docker-compose up -d postgres pgadmin", "db:stop": "docker-compose down", "db:reset": "docker-compose down -v && ./scripts/setup-database.sh", "db:import": "tsx scripts/import-questions.ts", "db:studio": "prisma studio", "start:full": "./scripts/start-system.sh", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "docker:start": "./start-docker.sh", "docker:admin": "docker-compose --profile admin up -d"}, "dependencies": {"@prisma/client": "^6.11.0", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.4", "prisma": "^6.11.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}