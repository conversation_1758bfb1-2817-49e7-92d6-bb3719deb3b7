#!/bin/sh
set -e

echo "🚀 启动AWS考试系统..."

# 等待数据库就绪
echo "⏳ 等待数据库连接..."
until npx prisma db push --accept-data-loss; do
  echo "数据库未就绪，等待5秒后重试..."
  sleep 5
done

echo "✅ 数据库连接成功"

# 检查是否需要导入数据
echo "🔍 检查数据库数据..."
QUESTION_COUNT=$(npx prisma db execute --stdin <<< "SELECT COUNT(*) FROM questions;" 2>/dev/null | grep -o '[0-9]*' | head -1 || echo "0")

if [ "$QUESTION_COUNT" = "0" ] || [ -z "$QUESTION_COUNT" ]; then
  echo "📚 导入题库数据..."
  npx tsx scripts/import-questions.ts
  echo "✅ 数据导入完成"
else
  echo "✅ 数据库已包含 $QUESTION_COUNT 道题目"
fi

echo "🎉 启动Next.js应用..."
exec node server.js
