#!/bin/bash

# AWS考试系统数据库设置脚本
echo "🚀 开始设置AWS考试系统数据库..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down

# 启动PostgreSQL服务
echo "🐘 启动PostgreSQL服务..."
docker-compose up -d postgres

# 等待PostgreSQL启动
echo "⏳ 等待PostgreSQL启动..."
sleep 10

# 检查PostgreSQL是否就绪
echo "🔍 检查PostgreSQL连接..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker-compose exec postgres pg_isready -U postgres -d aws_exam_system > /dev/null 2>&1; then
        echo "✅ PostgreSQL已就绪"
        break
    fi
    
    echo "⏳ 等待PostgreSQL启动... (尝试 $attempt/$max_attempts)"
    sleep 2
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ PostgreSQL启动超时"
    exit 1
fi

# 生成Prisma客户端
echo "🔧 生成Prisma客户端..."
pnpm prisma generate

# 推送数据库架构
echo "📊 推送数据库架构..."
pnpm prisma db push

# 导入考试数据
echo "📚 导入考试数据..."
pnpm tsx scripts/import-questions.ts

# 启动pgAdmin (可选)
echo "🔧 启动pgAdmin管理界面..."
docker-compose up -d pgadmin

echo ""
echo "🎉 数据库设置完成！"
echo ""
echo "📊 服务信息:"
echo "  PostgreSQL: localhost:5432"
echo "  数据库名: aws_exam_system"
echo "  用户名: postgres"
echo "  密码: postgres123"
echo ""
echo "🔧 pgAdmin管理界面:"
echo "  URL: http://localhost:8080"
echo "  邮箱: <EMAIL>"
echo "  密码: admin123"
echo ""
echo "🚀 现在可以启动应用程序:"
echo "  pnpm dev"
