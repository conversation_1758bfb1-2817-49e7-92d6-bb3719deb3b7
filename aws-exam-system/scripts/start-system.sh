#!/bin/bash

# AWS考试系统快速启动脚本
echo "🚀 启动AWS考试系统..."

# 检查是否首次运行
if [ ! -f ".database-initialized" ]; then
    echo "🔧 检测到首次运行，开始初始化数据库..."
    ./scripts/setup-database.sh
    
    if [ $? -eq 0 ]; then
        touch .database-initialized
        echo "✅ 数据库初始化完成"
    else
        echo "❌ 数据库初始化失败"
        exit 1
    fi
else
    echo "📊 启动现有数据库服务..."
    docker-compose up -d postgres pgadmin
    
    # 等待数据库就绪
    echo "⏳ 等待数据库就绪..."
    sleep 5
fi

# 启动开发服务器
echo "🌐 启动Next.js开发服务器..."
pnpm dev
