#!/usr/bin/env python3
"""
生成AWS题库总结报告
"""

import json
from collections import defaultdict

def generate_summary_report():
    # 读取解析结果
    with open("aws_quiz_questions.json", "r", encoding="utf-8") as f:
        questions = json.load(f)
    
    print("=" * 80)
    print("AWS SAA-C03 题库分析报告")
    print("=" * 80)
    
    # 基本统计
    total_questions = len(questions)
    single_choice = sum(1 for q in questions if not q["is_multiple_choice"])
    multiple_choice = sum(1 for q in questions if q["is_multiple_choice"])
    
    print(f"\n📊 基本统计:")
    print(f"总题目数量: {total_questions}")
    print(f"单选题: {single_choice} ({single_choice/total_questions*100:.1f}%)")
    print(f"多选题: {multiple_choice} ({multiple_choice/total_questions*100:.1f}%)")
    
    # 按Topic统计
    topic_stats = defaultdict(lambda: {"total": 0, "single": 0, "multiple": 0})
    for q in questions:
        topic = q["topic"]
        topic_stats[topic]["total"] += 1
        if q["is_multiple_choice"]:
            topic_stats[topic]["multiple"] += 1
        else:
            topic_stats[topic]["single"] += 1
    
    print(f"\n📚 按Topic分布:")
    for topic in sorted(topic_stats.keys(), key=lambda x: int(x.split()[1])):
        stats = topic_stats[topic]
        print(f"{topic}: {stats['total']} 题 (单选: {stats['single']}, 多选: {stats['multiple']})")
    
    # 多选题详细分析
    multiple_choice_questions = [q for q in questions if q["is_multiple_choice"]]
    if multiple_choice_questions:
        print(f"\n🔢 多选题分析:")
        choice_count_stats = defaultdict(int)
        for q in multiple_choice_questions:
            count = q["multiple_choice_count"]
            choice_count_stats[count] += 1
        
        for count in sorted(choice_count_stats.keys()):
            if count > 0:
                print(f"选择 {count} 个答案: {choice_count_stats[count]} 题")
            else:
                print(f"未明确指定数量: {choice_count_stats[count]} 题")
    
    # 选项数量分析
    option_count_stats = defaultdict(int)
    for q in questions:
        option_count = len(q["options"])
        option_count_stats[option_count] += 1
    
    print(f"\n📝 选项数量分布:")
    for count in sorted(option_count_stats.keys()):
        print(f"{count} 个选项: {option_count_stats[count]} 题")
    
    # 显示一些多选题示例
    print(f"\n🎯 多选题示例:")
    multiple_examples = [q for q in questions if q["is_multiple_choice"]][:5]
    for i, q in enumerate(multiple_examples, 1):
        print(f"\n{i}. {q['topic']} Question #{q['question_number']}")
        print(f"   选择数量: {q['multiple_choice_count']}")
        print(f"   题目: {q['question_text'][:100]}...")
        print(f"   选项: {len(q['options'])} 个")
        for j, option in enumerate(q['options'][:4]):  # 只显示前4个选项
            print(f"     {option[:80]}...")
    
    # 保存简化版数据
    simplified_data = []
    for q in questions:
        simplified_data.append({
            "topic": q["topic"],
            "question_number": q["question_number"],
            "question_text": q["question_text"],
            "options": q["options"],
            "is_multiple_choice": q["is_multiple_choice"],
            "multiple_choice_count": q["multiple_choice_count"]
        })
    
    with open("aws_quiz_simplified.json", "w", encoding="utf-8") as f:
        json.dump(simplified_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 简化版数据已保存到 aws_quiz_simplified.json")
    print(f"📄 完整数据在 aws_quiz_questions.json")

if __name__ == "__main__":
    generate_summary_report()
